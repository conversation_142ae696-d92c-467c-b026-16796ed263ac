[{"id": 1, "gigId": 5, "freelancerId": 29, "pitch": "I have extensive experience with React and GIS mapping solutions. I've built similar interactive map applications for tourism and urban planning projects. I can deliver a mobile-first solution that meets all your requirements.", "sampleLinks": ["https://portfolio.example.com/interactive-maps", "https://github.com/damini/react-gis-app"], "skills": ["React", "GIS", "Frontend Development", "Mobile-First Design"], "tools": ["React", "Mapbox", "Tailwind CSS", "JavaScript"], "submittedAt": "2025-07-13T10:30:00Z", "status": "accepted"}, {"id": 2, "gigId": 5, "freelancerId": 1, "pitch": "Frontend developer with 5+ years experience in React and mapping libraries. I specialize in creating responsive, accessible web applications with clean, maintainable code. I can deliver this project within your 5-week timeline.", "sampleLinks": ["https://portfolio.example.com/react-projects", "https://demo.mapapp.example.com"], "skills": ["React", "TypeScript", "Web Development", "API Integration"], "tools": ["React", "TypeScript", "Mapbox", "Tailwind CSS"], "submittedAt": "2025-07-12T14:15:00Z", "status": "accepted"}, {"id": 3, "gigId": 5, "freelancerId": 3, "pitch": "Specialized in interactive web applications and geospatial data visualization. I have a strong background in creating user-friendly interfaces for complex mapping systems. I'd love to contribute to this Lagos Parks project.", "sampleLinks": ["https://portfolio.example.com/gis-projects", "https://interactive-maps.example.com"], "skills": ["UI/UX Design", "Frontend Development", "Data Visualization", "GIS"], "tools": ["React", "D3.js", "Mapbox", "Figma"], "submittedAt": "2025-07-11T16:45:00Z", "status": "accepted"}, {"id": 4, "gigId": 6, "freelancerId": 11, "pitch": "Mobile app developer with 4+ years experience in React Native. I've built several location-based apps and have experience with park management systems. I can deliver a user-friendly app that enhances the park visitor experience.", "sampleLinks": ["https://portfolio.example.com/mobile-apps", "https://github.com/riya/park-finder-app"], "skills": ["React Native", "Mobile Development", "Firebase", "Maps Integration"], "tools": ["React Native", "Firebase", "Google Maps API", "Expo"], "submittedAt": "2025-07-12T09:15:00Z", "status": "accepted"}, {"id": 5, "gigId": 7, "freelancerId": 2, "pitch": "Graphic designer specializing in digital signage and content management systems. I have experience creating intuitive interfaces for non-technical users and understand the importance of accessibility in public spaces.", "sampleLinks": ["https://portfolio.example.com/digital-signage", "https://behance.net/jenny/signage-projects"], "skills": ["Digital Design", "Content Management", "UI Design", "Accessibility"], "tools": ["Figma", "Adobe Creative Suite", "Sketch", "InVision"], "submittedAt": "2025-07-13T14:30:00Z", "status": "accepted"}, {"id": 6, "gigId": 8, "freelancerId": 31, "pitch": "I am perfect for the required workflow", "sampleLinks": ["https://figma.com/jala"], "skills": ["Design"], "tools": ["React", "D3.js", "Python", "SQL"], "submittedAt": "2025-08-03T14:04:43.567Z", "status": "accepted"}, {"id": 7, "gigId": 4, "freelancerId": 5, "pitch": "I have extensive experience in voice data annotation and machine learning datasets. I've worked on similar projects for accent recognition systems and can ensure high accuracy in timestamp precision.", "sampleLinks": ["https://portfolio.example.com/voice-annotation", "https://github.com/shadecee/ml-datasets"], "skills": ["Data Annotation", "Voice Processing", "Machine Learning", "Quality Assurance"], "tools": ["Audacity", "Excel", "Python", "Praat"], "submittedAt": "2025-08-03T15:30:00Z"}, {"id": 8, "gigId": 10, "freelancerId": 11, "pitch": "I'm a React Native specialist with 6+ years of experience building mobile apps for field operations. I've developed similar offline-first applications for maintenance teams and understand the challenges of poor connectivity environments. I can deliver a robust, user-friendly solution.", "sampleLinks": ["https://portfolio.example.com/maintenance-apps", "https://github.com/freelancer11/offline-mobile-apps"], "skills": ["React Native", "TypeScript", "Offline-First Development", "Mobile UI/UX"], "tools": ["React Native", "TypeScript", "SQLite", "Redux Persist", "React Navigation"], "submittedAt": "2025-08-03T16:00:00Z", "status": "accepted"}, {"id": 999, "gigId": 11, "freelancerId": 11, "pitch": "I'm a UI/UX designer with 4+ years of experience creating conversion-focused landing pages. I specialize in modern, clean designs that drive results. I can deliver a mobile-first responsive design that aligns with your brand and business goals.", "sampleLinks": ["https://portfolio.example.com/landing-pages", "https://dribbble.com/designer/landing-designs"], "skills": ["UI/UX Design", "Web Design", "Figma", "Responsive Design", "Conversion Optimization"], "tools": ["Figma", "Adobe Creative Suite", "HTML/CSS", "Sketch"], "submittedAt": "2025-08-03T17:00:00Z", "status": "pending"}, {"id": 10, "gigId": 1, "freelancerId": 31, "pitch": "Test application", "sampleLinks": [], "skills": [], "tools": [], "submittedAt": "2025-08-03T20:31:20.862Z"}, {"id": 11, "gigId": 9, "freelancerId": 31, "pitch": "<PERSON> don cast, last last, na everybody go chop breakfast.", "sampleLinks": ["https://platform.openai.com/settings/organization/billing/overview"], "skills": ["marketing", "ui design"], "tools": ["Figma"], "submittedAt": "2025-08-03T22:19:53.645Z", "status": "accepted"}, {"id": 12, "gigId": 1, "freelancerId": 31, "pitch": "iuwehfcnodslnjerofvjesomcgssjemgoinrdesjnrafverhcjsnwihvgemcsjorfnheomsjgcirnhuesbfjvhdcmelf", "sampleLinks": ["https://uwaterloo.ca/the-centre/awards-and-financial-aid/student-bursaries"], "skills": ["marketing", "design"], "tools": ["Figma", "Adobe Illustrator"], "submittedAt": "2025-08-03T22:35:52.696Z"}, {"id": 13, "gigId": 10, "freelancerId": 31, "pitch": "I can ensure my design fixes ensures both the commissioner’s notification panel and review modal are functionally complete and aligned with ARTISH’s task submission flow. Let me know if you’d like to batch unread notifications or add timestampse", "sampleLinks": ["https://x.com/"], "skills": ["marketing,"], "tools": ["Figma"], "submittedAt": "2025-08-04T20:35:35.161Z", "status": "accepted"}, {"id": 14, "gigId": 2, "freelancerId": 1, "pitch": "I am the smartest and fastes graphic designer for work relating to Cinema 4D which can considerably bring designs to life.", "sampleLinks": ["https://substack.com/@thewavng?utm_source=about-page"], "skills": ["marketing", "design"], "tools": ["Adobe After Effects", "Illustrator"], "submittedAt": "2025-08-06T16:58:01.797Z", "status": "accepted"}, {"id": 101, "gigId": 12, "freelancerId": 5, "pitch": "Professional photographer with 8+ years of corporate event experience. I specialize in capturing key moments, networking sessions, and speaker presentations. My portfolio includes events for Fortune 500 companies and I'm comfortable working in various lighting conditions.", "sampleLinks": ["https://portfolio.example.com/corporate-events", "https://flickr.com/photos/nia-events"], "skills": ["Event Photography", "Corporate Photography", "Low-Light Photography", "Photo Editing"], "tools": ["Canon 5D Mark IV", "Adobe Lightroom", "Adobe Photoshop", "Professional Lighting Equipment"], "submittedAt": "2025-08-06T17:00:00.000Z", "status": "rejected"}, {"id": 102, "gigId": 12, "freelancerId": 8, "pitch": "Award-winning event photographer with expertise in corporate conferences and networking events. I have a keen eye for candid moments and professional headshots. My work has been featured in several business publications.", "sampleLinks": ["https://portfolio.example.com/business-events", "https://instagram.com/kemi_photography"], "skills": ["Event Photography", "Portrait Photography", "Business Photography", "Photo Retouching"], "tools": ["Nikon D850", "Adobe Creative Suite", "Capture One", "Professional Flash Equipment"], "submittedAt": "2025-08-06T17:01:40.000Z", "status": "pending"}, {"id": 103, "gigId": 13, "freelancerId": 14, "pitch": "3D visualization specialist with extensive experience in architectural and event space modeling. I've created detailed venue visualizations for major conferences and corporate events. My work helps clients optimize space utilization and plan logistics effectively.", "sampleLinks": ["https://portfolio.example.com/3d-venues", "https://behance.net/liam-3d-design"], "skills": ["3D Modeling", "Architectural Visualization", "Event Planning", "Space Design"], "tools": ["<PERSON><PERSON>der", "SketchUp", "AutoCAD", "V-Ray", "Adobe After Effects"], "submittedAt": "2025-08-06T17:03:20.000Z", "status": "pending"}, {"id": 104, "gigId": 12, "freelancerId": 22, "pitch": "Creative photographer specializing in corporate events and brand storytelling. I focus on capturing the energy and atmosphere of business gatherings while maintaining professional quality. My approach combines documentary-style photography with artistic composition.", "sampleLinks": ["https://portfolio.example.com/corporate-stories", "https://500px.com/yuki-photography"], "skills": ["Event Photography", "Brand Photography", "Documentary Photography", "Creative Composition"], "tools": ["Sony A7R IV", "Adobe Lightroom", "Adobe Photoshop", "Drone Photography Equipment"], "submittedAt": "2025-08-06T17:05:00.000Z", "status": "rejected", "rejectedAt": "2025-08-07T01:20:30.335Z"}, {"id": 19, "gigId": 13, "freelancerId": 1, "pitch": "I am well-suited for projects that require this level of detail.", "sampleLinks": ["https://substack.com/@thewavng?utm_source=about-page"], "skills": ["deisgn", "animation"], "tools": ["<PERSON><PERSON>der", "SketchUp", "AutoCAD", "Adobe Creative Suite"], "submittedAt": "2025-08-07T00:23:19.110Z", "status": "rejected"}, {"id": 20, "gigId": 13, "freelancerId": 1, "pitch": "For years, Republicans’ attitudes towards Israel were dominated by evangelicals’ affection for Israel. Has that changed? <PERSON> used the word “genocide” in describing Israel’s assault on Gaza. And, in general, young Republicans are less willing to support the Jewish state unquestioningly, leading to a slow but steady decline in GOP support.", "sampleLinks": ["https://substack.com/@thewavng?utm_source=about-page"], "skills": ["animation", "design"], "tools": ["<PERSON><PERSON>der", "SketchUp", "AutoCAD", "Adobe Creative Suite"], "submittedAt": "2025-08-07T01:05:10.126Z", "status": "rejected", "rejectedAt": "2025-08-07T01:05:58.297Z"}, {"id": 21, "gigId": 12, "freelancerId": 1, "pitch": "As noble as <PERSON><PERSON>’s call for bipartisanship is, the senator must take a hard look at whether he is being played for a fool by the GOP.  If the situation were reversed, and Republicans held 50 seats in the U.S. Senate along with the tie-breaking vice presidential vote, would they be seeking bipartisanship?", "sampleLinks": ["https://www.bbc.com/news/"], "skills": ["Graphic Design"], "tools": ["Professional Camera Equipment", "Adobe Lightroom", "Adobe Photoshop"], "submittedAt": "2025-08-07T17:48:13.387Z", "status": "accepted"}]