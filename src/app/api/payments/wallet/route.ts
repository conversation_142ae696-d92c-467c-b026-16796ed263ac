

import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import fs from "fs";
import path from "path";

const walletsFile = path.join(process.cwd(), "data/payments/wallets.json");

// Helper to load wallets from JSON file
function loadWallets() {
  try {
    if (!fs.existsSync(walletsFile)) return [];
    const data = fs.readFileSync(walletsFile, "utf-8");
    return JSON.parse(data);
  } catch (e) {
    return [];
  }
}

// Helper to save wallets to JSON file
function saveWallets(wallets: any[]) {
  fs.writeFileSync(walletsFile, JSON.stringify(wallets, null, 2), "utf-8");
}

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session || !session.user || !session.user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    const userId = Number(session.user.id);
    const userType = (session.user as any).type || "freelancer";
    let wallets = loadWallets();
    let wallet = wallets.find((w: any) => w.userId === userId);
    if (!wallet) {
      wallet = {
        userId,
        userType,
        currency: "USD",
        availableBalance: 0,
        pendingWithdrawals: 0,
        lifetimeEarnings: 0,
        totalWithdrawn: 0,
        updatedAt: new Date().toISOString(),
      };
      wallets.push(wallet);
      saveWallets(wallets);
    }
    return NextResponse.json(wallet);
  } catch (err: any) {
    return NextResponse.json({ error: err?.message || "Server error" }, { status: 500 });
  }
}