import { NextResponse } from 'next/server';
import { appendTransaction, type TransactionRecord } from '@/app/api/payments/repos/transactions-repo';
import { getWallet, upsertWallet } from '@/app/api/payments/repos/wallets-repo';

import type { Wallet, Currency } from '@/app/api/payments/repos/wallets-repo';

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { orderId, productId, freelancerId, amount, currency } = body;

    // Accept any ISO 4217 currency string; default to 'USD' if not provided
    const cur: Currency = (typeof currency === 'string' && currency.trim().length > 0)
      ? (currency as string)
      : 'USD';

    // Validation
    if (
      !orderId ||
      !productId ||
      !freelancerId ||
      typeof amount !== 'number' ||
      amount <= 0
    ) {
      return NextResponse.json(
        { error: 'Missing or invalid required fields.' },
        { status: 400 }
      );
    }

    // Create transaction object (Transactions repo shape)
    const transaction: TransactionRecord = {
      transactionId: `TXN-${orderId}`,
      type: 'store-purchase',
      integration: 'mock',
      status: 'paid',
      amount: Number(amount),
      timestamp: new Date().toISOString(),
      freelancerId: Number(freelancerId),
      productId: String(productId),
      metadata: { orderId: String(orderId) }
    };

    await appendTransaction(transaction);

    // Get or create wallet (Wallets repo shape)
    const nowISO = new Date().toISOString();
    let walletMaybe: Wallet | undefined = await getWallet(Number(freelancerId), 'freelancer', cur);
    if (!walletMaybe) {
      walletMaybe = {
        userId: Number(freelancerId),
        userType: 'freelancer',
        currency: cur,
        availableBalance: 0,
        pendingWithdrawals: 0,
        totalWithdrawn: 0,
        lifetimeEarnings: 0,
        holds: 0,
        updatedAt: nowISO,
      } as Wallet;
    }

    const wallet: Wallet = walletMaybe as Wallet;
    wallet.availableBalance = Number(wallet.availableBalance) + Number(amount);
    wallet.lifetimeEarnings = Number(wallet.lifetimeEarnings) + Number(amount);
    wallet.updatedAt = nowISO;

    await upsertWallet(wallet);

    return NextResponse.json({
      message: 'Storefront payment credited',
      transactionId: transaction.transactionId,
      wallet: {
        availableBalance: wallet.availableBalance,
        lifetimeEarnings: wallet.lifetimeEarnings,
        currency: wallet.currency
      }
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}