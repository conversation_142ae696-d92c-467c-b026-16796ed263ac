import { NextResponse } from 'next/server';
import { appendTransaction, type TransactionRecord, findByMetadataKey } from '@/app/api/payments/repos/transactions-repo';
import { getWallet, upsertWallet } from '@/app/api/payments/repos/wallets-repo';
import { ok, err, RefreshHints, ErrorCodes } from '@/lib/http/envelope';
import { logWalletChange } from '@/lib/log/transitions';

import type { Wallet, Currency } from '@/app/api/payments/repos/wallets-repo';

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { orderId, productId, freelancerId, amount, currency } = body;

    // Accept any ISO 4217 currency string; default to 'USD' if not provided
    const cur: Currency = (typeof currency === 'string' && currency.trim().length > 0)
      ? (currency as string)
      : 'USD';

    // Validation
    if (
      !orderId ||
      !productId ||
      !freelancerId ||
      typeof amount !== 'number' ||
      amount <= 0
    ) {
      return NextResponse.json(
        err({
          code: ErrorCodes.INVALID_INPUT,
          message: 'Missing or invalid required fields: orderId, productId, freelancerId, amount',
          status: 400,
        }),
        { status: 400 }
      );
    }

    // 🔒 Idempotency check: look for existing transaction with this orderId
    const existingTransactions = await findByMetadataKey('orderId', String(orderId));
    if (existingTransactions.length > 0) {
      const existingTx = existingTransactions[0];

      // Get current wallet state for response
      const wallet = await getWallet(Number(freelancerId), 'freelancer', cur);

      return NextResponse.json(
        ok({
          entities: {
            transaction: existingTx,
            wallet: wallet ? {
              availableBalance: wallet.availableBalance,
              lifetimeEarnings: wallet.lifetimeEarnings,
              currency: wallet.currency,
            } : null,
          },
          refreshHints: [RefreshHints.WALLET_SUMMARY, RefreshHints.TRANSACTIONS_LIST],
          notificationsQueued: true,
          message: 'Idempotent success (already credited)',
        })
      );
    }

    // Create transaction object (Transactions repo shape)
    const nowISO = new Date().toISOString();
    const transaction: TransactionRecord = {
      transactionId: `TXN-${orderId}`,
      type: 'store-purchase',
      integration: 'mock',
      status: 'paid',
      amount: Number(amount),
      timestamp: nowISO,
      currency: cur,
      freelancerId: Number(freelancerId),
      productId: String(productId),
      metadata: { orderId: String(orderId) }
    };

    await appendTransaction(transaction);

    // Get or create wallet (Wallets repo shape)
    let walletMaybe: Wallet | undefined = await getWallet(Number(freelancerId), 'freelancer', cur);
    if (!walletMaybe) {
      walletMaybe = {
        userId: Number(freelancerId),
        userType: 'freelancer',
        currency: cur,
        availableBalance: 0,
        pendingWithdrawals: 0,
        totalWithdrawn: 0,
        lifetimeEarnings: 0,
        holds: 0,
        updatedAt: nowISO,
      } as Wallet;
    }

    const previousBalance = walletMaybe.availableBalance;
    const wallet: Wallet = walletMaybe as Wallet;
    wallet.availableBalance = Number(wallet.availableBalance) + Number(amount);
    wallet.lifetimeEarnings = Number(wallet.lifetimeEarnings) + Number(amount);
    wallet.updatedAt = nowISO;

    await upsertWallet(wallet);

    // Log the wallet change for observability
    logWalletChange(
      Number(freelancerId),
      'freelancer',
      'credit',
      Number(amount),
      cur,
      Number(freelancerId), // Self-initiated storefront credit
      {
        reason: 'storefront_purchase',
        transactionId: transaction.transactionId,
        previousBalance,
        newBalance: wallet.availableBalance,
      }
    );

    return NextResponse.json(
      ok({
        entities: {
          transaction: {
            transactionId: transaction.transactionId,
            amount: transaction.amount,
            currency: transaction.currency,
            status: transaction.status,
            timestamp: transaction.timestamp,
          },
          wallet: {
            availableBalance: wallet.availableBalance,
            lifetimeEarnings: wallet.lifetimeEarnings,
            currency: wallet.currency,
          },
        },
        refreshHints: [
          RefreshHints.WALLET_SUMMARY,
          RefreshHints.TRANSACTIONS_LIST,
          RefreshHints.DASHBOARD,
        ],
        notificationsQueued: false, // Storefront credits don't generate notifications
        message: 'Storefront payment credited successfully',
      })
    );
  } catch (error) {
    console.error('[STOREFRONT_CREDIT_ERROR]', error);
    return NextResponse.json(
      err({
        code: ErrorCodes.INTERNAL_ERROR,
        message: 'Internal server error',
        status: 500,
      }),
      { status: 500 }
    );
  }
}