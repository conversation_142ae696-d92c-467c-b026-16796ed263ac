import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getInvoiceByNumber, updateInvoice } from '@/app/api/payments/repos/invoices-repo';
import { listByInvoiceNumber, appendTransaction, updateTransaction } from '@/app/api/payments/repos/transactions-repo';
import { processMockPayment } from '../utils/gateways/test-gateway';
import { getWallet, upsertWallet } from '@/app/api/payments/repos/wallets-repo';
import { PaymentsService } from '@/app/api/payments/services/payments-service';
import type { InvoiceLike } from '@/app/api/payments/domain/types';

// Minimal event emitter stub (replace with real bus later)
async function emit(event: string, payload: any) {
  try {
    console.log(`[event] ${event}`, payload);
  } catch (e) {
    // noop for now
  }
}

// Gateway flags (future real integrations)
const useStripe = process.env.PAYMENT_GATEWAY_STRIPE === 'true';
const usePaystack = process.env.PAYMENT_GATEWAY_PAYSTACK === 'true';
const usePayPal = process.env.PAYMENT_GATEWAY_PAYPAL === 'true';

export async function POST(req: Request) {
  try {
    // 🔒 Auth
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { invoiceNumber, commissionerId } = await req.json();
    const sessionUserId = parseInt(session.user.id);

    // Validate body
    if (!invoiceNumber || !commissionerId) {
      return NextResponse.json({ error: 'Missing invoiceNumber or commissionerId' }, { status: 400 });
    }

    // 🔒 Ensure commissioner matches session
    if (Number(commissionerId) !== sessionUserId) {
      return NextResponse.json({ error: 'Unauthorized: You can only execute payments for your own invoices' }, { status: 403 });
    }

    // Load invoice
    const invRaw = await getInvoiceByNumber(String(invoiceNumber));
    if (!invRaw) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    // 🔒 Ensure commissioner owns invoice
    if (Number(invRaw.commissionerId) !== Number(commissionerId)) {
      return NextResponse.json({ error: 'Unauthorized commissioner' }, { status: 403 });
    }

    // Idempotency / status guards
    if (invRaw.status === 'paid') {
      return NextResponse.json({ error: 'Invoice already paid' }, { status: 409 });
    }

    // Shape to domain DTO
    const invoice: InvoiceLike = {
      invoiceNumber: String(invRaw.invoiceNumber),
      projectId: Number(invRaw.projectId ?? 0),
      freelancerId: Number(invRaw.freelancerId),
      commissionerId: Number(invRaw.commissionerId),
      totalAmount: Number(invRaw.totalAmount),
      currency: (invRaw as any).currency,
      status: invRaw.status,
      method: (invRaw as any).method || 'milestone',
      milestoneNumber: (invRaw as any).milestoneNumber,
      issueDate: (invRaw as any).issueDate,
      dueDate: (invRaw as any).dueDate,
      paidDate: (invRaw as any).paidDate,
    };

    // ✅ Service-layer rule: execute requires 'processing' by default
    const canExec = PaymentsService.canExecutePayment(invoice, Number(commissionerId));
    if (!canExec.ok) {
      return NextResponse.json({ error: canExec.reason }, { status: 400 });
    }

    // Gateway placeholders (real integrations later)
    let paymentRecord: any = null;
    if (useStripe) {
      console.log('[payments.execute] Stripe placeholder');
      paymentRecord = { transactionId: `TXN-${invoice.invoiceNumber}` };
    } else if (usePaystack) {
      console.log('[payments.execute] Paystack placeholder');
      paymentRecord = { transactionId: `TXN-${invoice.invoiceNumber}` };
    } else if (usePayPal) {
      console.log('[payments.execute] PayPal placeholder');
      paymentRecord = { transactionId: `TXN-${invoice.invoiceNumber}` };
    } else {
      console.log('[payments.execute] Using mock gateway');
      paymentRecord = await processMockPayment({
        invoiceNumber: invoice.invoiceNumber,
        projectId: Number(invoice.projectId ?? 0),
        freelancerId: Number(invoice.freelancerId),
        commissionerId: Number(commissionerId),
        totalAmount: Number(invoice.totalAmount)
      }, 'execute');
    }

    // Update invoice → paid
    const paidDate = new Date().toISOString();
    const invoiceUpdated = await updateInvoice(String(invoiceNumber), {
      status: 'paid',
      paidDate,
      updatedAt: paidDate
    });
    if (!invoiceUpdated) {
      return NextResponse.json({ error: 'Failed to update invoice status' }, { status: 500 });
    }

    // Update transaction log
    const existingTxs = await listByInvoiceNumber(String(invoiceNumber));
    const latestTx = existingTxs[existingTxs.length - 1];
    const integrationMethod = useStripe ? 'stripe' : usePaystack ? 'paystack' : usePayPal ? 'paypal' : 'mock';
    const timestamp = new Date().toISOString();

    if (latestTx && latestTx.transactionId) {
      await updateTransaction(latestTx.transactionId, {
        status: 'paid',
        timestamp,
        metadata: {
          ...(latestTx.metadata || {}),
          executedBy: commissionerId,
        }
      });
    } else {
      // No prior tx found — append a new paid record (service can build this too)
      const fallbackTx = PaymentsService.buildTransaction({
        invoiceNumber: invoice.invoiceNumber,
        projectId: invoice.projectId,
        freelancerId: invoice.freelancerId,
        commissionerId: invoice.commissionerId,
        totalAmount: invoice.totalAmount,
      }, 'execute', integrationMethod as any);

      await appendTransaction(fallbackTx as any);
    }

    // Credit freelancer wallet with the paid amount (multi-currency)
    const amountPaid = Number(invoice.totalAmount);
    const freelancerIdNum = Number(invoice.freelancerId);
    const currency = String((invoice as any).currency || 'USD');
    const nowISO = new Date().toISOString();

    let wallet = await getWallet(freelancerIdNum, 'freelancer', currency);
    if (!wallet) {
      wallet = {
        userId: freelancerIdNum,
        userType: 'freelancer',
        currency,
        availableBalance: 0,
        pendingWithdrawals: 0,
        totalWithdrawn: 0,
        lifetimeEarnings: 0,
        holds: 0,
        updatedAt: nowISO,
      };
    }

    wallet.availableBalance = Number(wallet.availableBalance) + amountPaid;
    wallet.lifetimeEarnings = Number(wallet.lifetimeEarnings) + amountPaid;
    wallet.updatedAt = nowISO;

    await upsertWallet(wallet);

    // 🔔 Emit event for notifications/UI refresh hooks
    await emit('invoice.paid', {
      invoiceNumber: invoice.invoiceNumber,
      projectId: invoice.projectId,
      freelancerId: invoice.freelancerId,
      commissionerId: invoice.commissionerId,
      amount: invoice.totalAmount,
      currency,
      transactionId: paymentRecord.transactionId,
      paidDate,
    });

    return NextResponse.json({
      message: 'Payment executed successfully',
      status: 'paid',
      invoiceNumber,
      transactionId: paymentRecord.transactionId,
      amount: invoice.totalAmount,
      paidDate,
      integration: integrationMethod,
      wallet: {
        availableBalance: wallet.availableBalance,
        pendingWithdrawals: wallet.pendingWithdrawals,
        totalWithdrawn: wallet.totalWithdrawn,
        lifetimeEarnings: wallet.lifetimeEarnings,
        currency: wallet.currency,
      },
      refreshHints: ['wallet:summary','invoices:list','transactions:list','projects:overview']
    });
  } catch (error) {
    console.error('[PAYMENT_EXECUTE_ERROR]', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
