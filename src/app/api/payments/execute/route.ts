import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getInvoiceByNumber, updateInvoice } from '@/app/api/payments/repos/invoices-repo';
import { listByInvoiceNumber, appendTransaction, updateTransaction } from '@/app/api/payments/repos/transactions-repo';
import { processMockPayment } from '../utils/gateways/test-gateway';
import { getWallet, upsertWallet } from '@/app/api/payments/repos/wallets-repo';
import { PaymentsService } from '@/app/api/payments/services/payments-service';
import { ok, err, RefreshHints, ErrorCodes } from '@/lib/http/envelope';
import { logInvoiceTransition, logWalletChange } from '@/lib/log/transitions';
import { emit as emitBus } from '@/lib/events/bus';
import type { InvoiceLike } from '@/app/api/payments/domain/types';



// Gateway flags (future real integrations)
const useStripe = process.env.PAYMENT_GATEWAY_STRIPE === 'true';
const usePaystack = process.env.PAYMENT_GATEWAY_PAYSTACK === 'true';
const usePayPal = process.env.PAYMENT_GATEWAY_PAYPAL === 'true';

export async function POST(req: Request) {
  try {
    // 🔒 Auth
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        err({
          code: ErrorCodes.UNAUTHORIZED,
          message: 'Authentication required',
          status: 401,
        }),
        { status: 401 }
      );
    }

    const { invoiceNumber, commissionerId } = await req.json();
    const sessionUserId = parseInt(session.user.id);

    // Validate body
    if (!invoiceNumber || !commissionerId) {
      return NextResponse.json(
        err({
          code: ErrorCodes.MISSING_REQUIRED_FIELD,
          message: 'Missing invoiceNumber or commissionerId',
          status: 400,
        }),
        { status: 400 }
      );
    }

    // 🔒 Ensure commissioner matches session
    if (Number(commissionerId) !== sessionUserId) {
      return NextResponse.json(
        err({
          code: ErrorCodes.FORBIDDEN,
          message: 'Unauthorized: You can only execute payments for your own invoices',
          status: 403,
        }),
        { status: 403 }
      );
    }

    // Load invoice
    const invRaw = await getInvoiceByNumber(String(invoiceNumber));
    if (!invRaw) {
      return NextResponse.json(
        err({
          code: ErrorCodes.RESOURCE_NOT_FOUND,
          message: 'Invoice not found',
          status: 404,
        }),
        { status: 404 }
      );
    }

    // 🔒 Ensure commissioner owns invoice
    if (Number(invRaw.commissionerId) !== Number(commissionerId)) {
      return NextResponse.json(
        err({
          code: ErrorCodes.FORBIDDEN,
          message: 'Unauthorized commissioner',
          status: 403,
        }),
        { status: 403 }
      );
    }

    // Idempotency / status guards
    if (invRaw.status === 'paid') {
      return NextResponse.json(
        err({
          code: ErrorCodes.DUPLICATE_REQUEST,
          message: 'Invoice already paid',
          status: 409,
        }),
        { status: 409 }
      );
    }

    // Shape to domain DTO
    const invoice: InvoiceLike = {
      invoiceNumber: String(invRaw.invoiceNumber),
      projectId: Number(invRaw.projectId ?? 0),
      freelancerId: Number(invRaw.freelancerId),
      commissionerId: Number(invRaw.commissionerId),
      totalAmount: Number(invRaw.totalAmount),
      currency: (invRaw as any).currency || 'USD',
      status: invRaw.status,
      method: (invRaw as any).method || 'milestone',
      milestoneNumber: (invRaw as any).milestoneNumber,
      issueDate: (invRaw as any).issueDate,
      dueDate: (invRaw as any).dueDate,
      paidDate: (invRaw as any).paidDate,
    };

    // ✅ Service-layer rule: execute requires 'processing' by default
    const canExec = PaymentsService.canExecutePayment(invoice, Number(commissionerId));
    if (!canExec.ok) {
      return NextResponse.json(
        err({
          code: ErrorCodes.INVALID_STATUS,
          message: canExec.reason,
          status: 400,
        }),
        { status: 400 }
      );
    }

    // Gateway placeholders (real integrations later)
    let paymentRecord: any = null;
    if (useStripe) {
      console.log('[payments.execute] Stripe placeholder');
      paymentRecord = { transactionId: `TXN-${invoice.invoiceNumber}` };
    } else if (usePaystack) {
      console.log('[payments.execute] Paystack placeholder');
      paymentRecord = { transactionId: `TXN-${invoice.invoiceNumber}` };
    } else if (usePayPal) {
      console.log('[payments.execute] PayPal placeholder');
      paymentRecord = { transactionId: `TXN-${invoice.invoiceNumber}` };
    } else {
      console.log('[payments.execute] Using mock gateway');
      paymentRecord = await processMockPayment({
        invoiceNumber: invoice.invoiceNumber,
        projectId: Number(invoice.projectId ?? 0),
        freelancerId: Number(invoice.freelancerId),
        commissionerId: Number(commissionerId),
        totalAmount: Number(invoice.totalAmount)
      }, 'execute');
    }

    // Update invoice → paid
    const paidDate = new Date().toISOString();
    const invoiceUpdated = await updateInvoice(String(invoiceNumber), {
      status: 'paid',
      paidDate,
      updatedAt: paidDate
    });
    if (!invoiceUpdated) {
      return NextResponse.json({ error: 'Failed to update invoice status' }, { status: 500 });
    }

    // Update transaction log
    const existingTxs = await listByInvoiceNumber(String(invoiceNumber));
    const latestTx = existingTxs[existingTxs.length - 1];
    const integrationMethod = useStripe ? 'stripe' : usePaystack ? 'paystack' : usePayPal ? 'paypal' : 'mock';
    const timestamp = new Date().toISOString();

    if (latestTx && latestTx.transactionId) {
      await updateTransaction(latestTx.transactionId, {
        status: 'paid',
        timestamp,
        currency: invoice.currency,
        metadata: {
          ...(latestTx.metadata || {}),
          executedBy: commissionerId,
        }
      });
    } else {
      // No prior tx found — append a new paid record
      const fallbackTx = PaymentsService.buildTransaction({
        invoiceNumber: invoice.invoiceNumber,
        projectId: invoice.projectId,
        freelancerId: invoice.freelancerId,
        commissionerId: invoice.commissionerId,
        totalAmount: invoice.totalAmount,
      }, 'execute', integrationMethod as any);

      // Add currency to transaction
      (fallbackTx as any).currency = invoice.currency;
      await appendTransaction(fallbackTx as any);
    }

    // Credit freelancer wallet with the paid amount (multi-currency)
    const amountPaid = Number(invoice.totalAmount);
    const freelancerIdNum = Number(invoice.freelancerId);
    const currency = String(invoice.currency || 'USD');
    const nowISO = new Date().toISOString();

    let wallet = await getWallet(freelancerIdNum, 'freelancer', currency);
    const previousBalance = wallet?.availableBalance || 0;

    if (!wallet) {
      wallet = {
        userId: freelancerIdNum,
        userType: 'freelancer',
        currency,
        availableBalance: 0,
        pendingWithdrawals: 0,
        totalWithdrawn: 0,
        lifetimeEarnings: 0,
        holds: 0,
        updatedAt: nowISO,
      };
    }

    wallet.availableBalance = Number(wallet.availableBalance) + amountPaid;
    wallet.lifetimeEarnings = Number(wallet.lifetimeEarnings) + amountPaid;
    wallet.updatedAt = nowISO;

    await upsertWallet(wallet);

    // Log transitions for observability
    logInvoiceTransition(
      invoice.invoiceNumber,
      invRaw.status,
      'paid',
      Number(commissionerId),
      {
        projectId: invoice.projectId,
        amount: invoice.totalAmount,
        currency: invoice.currency,
        integration: integrationMethod,
        transactionId: paymentRecord.transactionId,
      }
    );

    logWalletChange(
      freelancerIdNum,
      'freelancer',
      'credit',
      amountPaid,
      currency,
      Number(commissionerId),
      {
        reason: 'invoice_payment',
        transactionId: paymentRecord.transactionId,
        invoiceNumber: invoice.invoiceNumber,
        previousBalance,
        newBalance: wallet.availableBalance,
      }
    );

    // 🔔 Emit event for notifications/UI refresh hooks
    try {
      await emitBus('invoice.paid', {
        actorId: Number(commissionerId),
        targetId: Number(invoice.freelancerId),
        projectId: Number(invoice.projectId),
        invoiceNumber: invoice.invoiceNumber,
        amount: invoice.totalAmount,
        projectTitle: undefined, // Could be fetched if needed
      });
    } catch (e) {
      console.warn('[payments.execute] bus emit failed:', e);
    }

    return NextResponse.json(
      ok({
        entities: {
          invoice: {
            invoiceNumber,
            status: 'paid',
            amount: invoice.totalAmount,
            currency: invoice.currency,
            paidDate,
          },
          transaction: {
            transactionId: paymentRecord.transactionId,
            integration: integrationMethod,
            status: 'paid',
            amount: invoice.totalAmount,
            currency: invoice.currency,
          },
          wallet: {
            availableBalance: wallet.availableBalance,
            pendingWithdrawals: wallet.pendingWithdrawals,
            totalWithdrawn: wallet.totalWithdrawn,
            lifetimeEarnings: wallet.lifetimeEarnings,
            currency: wallet.currency,
          },
        },
        refreshHints: [
          RefreshHints.WALLET_SUMMARY,
          RefreshHints.INVOICES_LIST,
          RefreshHints.TRANSACTIONS_LIST,
          RefreshHints.PROJECTS_OVERVIEW,
          RefreshHints.DASHBOARD,
        ],
        notificationsQueued: true,
        message: 'Payment executed successfully',
      })
    );
  } catch (error) {
    console.error('[PAYMENT_EXECUTE_ERROR]', error);
    return NextResponse.json(
      err({
        code: ErrorCodes.INTERNAL_ERROR,
        message: 'Internal server error',
        status: 500,
      }),
      { status: 500 }
    );
  }
}
