

import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import path from 'path';
import fs from 'fs/promises';

const WALLETS_PATH = path.join(process.cwd(), 'data', 'payments', 'wallets.json');
const WITHDRAWALS_PATH = path.join(process.cwd(), 'data', 'payments', 'withdrawals.json');

async function readJsonSafe<T>(filePath: string, fallback: T): Promise<T> {
  try {
    const raw = await fs.readFile(filePath, 'utf-8');
    return JSON.parse(raw) as T;
  } catch {
    return fallback;
  }
}

async function writeJson(filePath: string, data: unknown) {
  await fs.mkdir(path.dirname(filePath), { recursive: true });
  await fs.writeFile(filePath, JSON.stringify(data, null, 2));
}

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { withdrawalId, processedById } = await req.json();
    if (!withdrawalId) {
      return NextResponse.json({ error: 'Missing withdrawalId' }, { status: 400 });
    }

    const wallets: any[] = await readJsonSafe<any[]>(WALLETS_PATH, []);
    const withdrawals: any[] = await readJsonSafe<any[]>(WITHDRAWALS_PATH, []);

    const idx = withdrawals.findIndex((w) => w.withdrawalId === String(withdrawalId));
    if (idx === -1) {
      return NextResponse.json({ error: 'Withdrawal not found' }, { status: 404 });
    }

    const record = withdrawals[idx];
    if (record.status !== 'pending') {
      return NextResponse.json({ error: 'Withdrawal is not pending' }, { status: 400 });
    }

    const wallet = wallets.find((x) => x.userId === record.userId && x.userType === record.userType);
    if (!wallet) {
      return NextResponse.json({ error: 'Wallet not found' }, { status: 404 });
    }

    // sanity guard: ensure pendingWithdrawals covers the amount
    if (Number(wallet.pendingWithdrawals || 0) < Number(record.amount)) {
      return NextResponse.json({ error: 'Insufficient pending balance to finalize' }, { status: 409 });
    }

    const now = new Date().toISOString();

    // move funds: pendingWithdrawals -> totalWithdrawn
    wallet.pendingWithdrawals = Number(wallet.pendingWithdrawals || 0) - Number(record.amount);
    wallet.totalWithdrawn = Number(wallet.totalWithdrawn || 0) + Number(record.amount);
    wallet.updatedAt = now;

    withdrawals[idx] = {
      ...record,
      status: 'paid',
      processedAt: now,
      processedById: processedById ?? Number(session.user.id)
    };

    await writeJson(WALLETS_PATH, wallets);
    await writeJson(WITHDRAWALS_PATH, withdrawals);

    return NextResponse.json({
      message: 'Withdrawal executed',
      withdrawalId: record.withdrawalId,
      status: 'paid',
      wallet: {
        availableBalance: wallet.availableBalance,
        pendingWithdrawals: wallet.pendingWithdrawals,
        totalWithdrawn: wallet.totalWithdrawn,
        currency: wallet.currency
      }
    });
  } catch (err) {
    console.error('[WITHDRAW_EXECUTE_ERROR]', err);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}