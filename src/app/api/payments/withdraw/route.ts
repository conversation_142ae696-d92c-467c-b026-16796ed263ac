

import { NextRequest, NextResponse } from "next/server";
import fs from "fs";
import path from "path";

const WALLETS_PATH = path.resolve(process.cwd(), "data/payments/wallets.json");
const WITHDRAWALS_PATH = path.resolve(process.cwd(), "data/payments/withdrawals.json");

type UserType = "freelancer" | "commissioner";
type Wallet = {
  userId: string;
  userType: UserType;
  currency: string;
  availableBalance: number;
  pendingWithdrawals: number;
  lifetimeEarnings: number;
  totalWithdrawn: number;
  updatedAt: string;
};

type Withdrawal = {
  withdrawalId: string;
  userId: string;
  userType: UserType;
  amount: number;
  status: "pending";
  requestedAt: string;
};

function readJSON(filePath: string): any[] {
  try {
    if (!fs.existsSync(filePath)) return [];
    const data = fs.readFileSync(filePath, "utf8");
    if (!data.trim()) return [];
    return JSON.parse(data);
  } catch (e) {
    return [];
  }
}

function writeJSON(filePath: string, data: any) {
  fs.mkdirSync(path.dirname(filePath), { recursive: true });
  fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    // In real app, get userId/userType from session/auth, here allow fallback to body for mock/testing
    const userId = body.userId;
    const userType: UserType = body.userType;
    const amount = body.amount;

    if (!userId || !userType || !["freelancer", "commissioner"].includes(userType)) {
      return NextResponse.json(
        { success: false, error: "Missing or invalid userId or userType" },
        { status: 400 }
      );
    }
    if (typeof amount !== "number" || isNaN(amount) || amount <= 0) {
      return NextResponse.json(
        { success: false, error: "Amount must be a positive number" },
        { status: 400 }
      );
    }

    // Load wallets
    const wallets: Wallet[] = readJSON(WALLETS_PATH);
    let wallet = wallets.find(
      (w) => w.userId === userId && w.userType === userType
    );
    if (!wallet) {
      wallet = {
        userId,
        userType,
        currency: "USD",
        availableBalance: 0,
        pendingWithdrawals: 0,
        lifetimeEarnings: 0,
        totalWithdrawn: 0,
        updatedAt: new Date().toISOString(),
      };
      wallets.push(wallet);
    }

    if (wallet.availableBalance < amount) {
      return NextResponse.json(
        { success: false, error: "Insufficient available balance" },
        { status: 400 }
      );
    }

    wallet.availableBalance -= amount;
    wallet.pendingWithdrawals += amount;
    wallet.updatedAt = new Date().toISOString();

    // Save updated wallet
    writeJSON(WALLETS_PATH, wallets);

    // Load withdrawals
    const withdrawals: Withdrawal[] = readJSON(WITHDRAWALS_PATH);
    const withdrawal: Withdrawal = {
      withdrawalId: `WD-${Date.now()}`,
      userId,
      userType,
      amount,
      status: "pending",
      requestedAt: new Date().toISOString(),
    };
    withdrawals.push(withdrawal);
    writeJSON(WITHDRAWALS_PATH, withdrawals);

    return NextResponse.json({ success: true, withdrawal });
  } catch (err: any) {
    if (
      err instanceof SyntaxError ||
      (err && typeof err.message === "string" && err.message.includes("JSON"))
    ) {
      return NextResponse.json(
        { success: false, error: "Invalid JSON" },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { success: false, error: "Unexpected error" },
      { status: 500 }
    );
  }
}