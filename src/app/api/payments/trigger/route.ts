import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getInvoiceByNumber, updateInvoice } from '@/app/api/payments/repos/invoices-repo';
import { appendTransaction, listByInvoiceNumber } from '@/app/api/payments/repos/transactions-repo';
import { readProject } from '@/lib/projects-utils';
import { processMockPayment } from '../utils/gateways/test-gateway';
import type { InvoiceLike, ProjectLike, TaskLike, InvoicingMethod, ProjectStatus } from '@/app/api/payments/domain/types';

// Optional env flags for future real gateways
const useStripe = process.env.PAYMENT_GATEWAY_STRIPE === 'true';
const usePaystack = process.env.PAYMENT_GATEWAY_PAYSTACK === 'true';
const usePayPal = process.env.PAYMENT_GATEWAY_PAYPAL === 'true';

export async function POST(req: Request) {
  try {
    // 🔒 Auth
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { invoiceNumber, freelancerId } = await req.json();
    const sessionUserId = parseInt(session.user.id);

    // Validate body
    if (!invoiceNumber || !freelancerId) {
      return NextResponse.json({ error: 'Missing invoiceNumber or freelancerId' }, { status: 400 });
    }

    // 🔒 Only the owner freelancer may trigger
    if (Number(freelancerId) !== sessionUserId) {
      return NextResponse.json({ error: 'Unauthorized: You can only trigger payments for your own invoices' }, { status: 403 });
    }

    // Load invoice
    const invoice = await getInvoiceByNumber(String(invoiceNumber));
    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    // 🔒 Ensure invoice belongs to freelancer
    if (Number(invoice.freelancerId) !== Number(freelancerId)) {
      return NextResponse.json({ error: 'Unauthorized freelancer' }, { status: 403 });
    }

    // Status guards
    if (invoice.status === 'paid') {
      return NextResponse.json({ error: 'Invoice already paid' }, { status: 409 });
    }
    if (invoice.status !== 'sent') {
      return NextResponse.json({ error: 'Invoice must be in "sent" status to trigger payment' }, { status: 400 });
    }

    // Project lookup
    if (!invoice.projectId) {
      return NextResponse.json({ error: 'Invoice has no associated project' }, { status: 400 });
    }
    const projectRaw = await readProject(Number(invoice.projectId));
    if (!projectRaw) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }

    // Normalize to domain unions (hard-narrow strings)
    const normalizedMethod = (projectRaw?.invoicingMethod === 'completion' ? 'completion' : 'milestone') as InvoicingMethod;

    const rawStatusVal = String(projectRaw?.status ?? 'ongoing');
    const allowedStatuses: ProjectStatus[] = ['proposed','ongoing','paused','completed','archived'];
    const normalizedStatus = (allowedStatuses as readonly string[]).includes(rawStatusVal)
      ? (rawStatusVal as ProjectStatus)
      : ('ongoing' as ProjectStatus);

    // 🔎 Call payment-eligibility endpoint first to ensure UI can safely show "Pay now"
    try {
      const proto = req.headers.get('x-forwarded-proto') ?? 'http';
      const host = req.headers.get('host') ?? 'localhost:3000';
      const base = `${proto}://${host}`;
      const eligRes = await fetch(`${base}/api/projects/payment-eligibility?projectId=${invoice.projectId}`, { method: 'GET', headers: { 'Content-Type': 'application/json' } });
      if (!eligRes.ok) {
        return NextResponse.json({ error: 'Failed to verify payment eligibility' }, { status: 502 });
      }
      const eligibility = await eligRes.json();
      if (!eligibility?.paymentEligible) {
        return NextResponse.json({ error: 'Project not eligible for payment yet', details: eligibility }, { status: 403 });
      }
    } catch (e) {
      console.warn('[payments.trigger] eligibility check failed', e);
      return NextResponse.json({ error: 'Eligibility check failed' }, { status: 502 });
    }

    // Prevent duplicate trigger by checking tx log first
    const existing = await listByInvoiceNumber(String(invoiceNumber));
    const hasOpenTx = existing.some(tx => tx.status === 'processing' || tx.status === 'paid');
    if (hasOpenTx) {
      return NextResponse.json({ error: 'Payment already triggered for this invoice', existingTransactionId: existing[0]?.transactionId }, { status: 409 });
    }

    // Gateway placeholders (real integrations later)
    if (useStripe) {
      console.log('[payments.trigger] Stripe placeholder');
    } else if (usePaystack) {
      console.log('[payments.trigger] Paystack placeholder');
    } else if (usePayPal) {
      console.log('[payments.trigger] PayPal placeholder');
    } else {
      console.log('[payments.trigger] Using mock gateway');
    }

    // Build transaction via mock gateway (processing)
    const paymentRecord = await processMockPayment({
      invoiceNumber: invoice.invoiceNumber,
      projectId: Number(invoice.projectId),
      freelancerId: Number(invoice.freelancerId),
      commissionerId: Number(invoice.commissionerId),
      totalAmount: Number(invoice.totalAmount)
    }, 'trigger');

    // Persist: set invoice → processing
    const ok = await updateInvoice(String(invoiceNumber), {
      status: 'processing',
      updatedAt: new Date().toISOString()
    });
    if (!ok) {
      return NextResponse.json({ error: 'Failed to update invoice status' }, { status: 500 });
    }

    // Persist: append transaction record
    await appendTransaction({
      ...paymentRecord,
      type: 'invoice',
      integration: 'mock'
    } as any);

    return NextResponse.json({
      message: 'Payment request initiated successfully',
      status: 'processing',
      transactionId: paymentRecord.transactionId,
      integration: paymentRecord.integration,
      invoiceNumber: invoice.invoiceNumber,
      amount: invoice.totalAmount,
      projectId: invoice.projectId
    });
  } catch (error) {
    console.error('[PAYMENT_TRIGGER_ERROR]', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}