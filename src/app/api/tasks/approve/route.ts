import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getTaskById, updateTask } from '@/app/api/payments/repos/tasks-repo';
import { emit as emitBus } from '@/lib/events/bus';
import { TaskService } from '@/app/api/tasks/services/task-service';
import { ok, err, RefreshHints, ErrorCodes } from '@/lib/http/envelope';
import { logTaskTransition } from '@/lib/log/transitions';

export async function POST(request: Request) {
  try {
    // 🔒 Auth
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        err({
          code: ErrorCodes.UNAUTHORIZED,
          message: 'Authentication required',
          status: 401,
        }),
        { status: 401 }
      );
    }

    const { taskId, projectId, commissionerId } = await request.json();

    if (!taskId || !projectId || !commissionerId) {
      return NextResponse.json(
        err({
          code: ErrorCodes.MISSING_REQUIRED_FIELD,
          message: 'Missing required fields: taskId, projectId, commissionerId',
          status: 400,
        }),
        { status: 400 }
      );
    }

    // Only the logged-in commissioner can approve
    if (Number(commissionerId) !== Number(session.user.id)) {
      return NextResponse.json(
        err({
          code: ErrorCodes.FORBIDDEN,
          message: 'Unauthorized: commissioner mismatch',
          status: 403,
        }),
        { status: 403 }
      );
    }

    // 1) Read from repo
    const task = await getTaskById(taskId);
    if (!task) {
      return NextResponse.json(
        err({
          code: ErrorCodes.RESOURCE_NOT_FOUND,
          message: 'Task not found',
          status: 404,
        }),
        { status: 404 }
      );
    }

    if (Number(task.projectId) !== Number(projectId)) {
      return NextResponse.json(
        err({
          code: ErrorCodes.INVALID_INPUT,
          message: 'Task does not belong to provided projectId',
          status: 400,
        }),
        { status: 400 }
      );
    }

    // 2) Use service for business logic
    let approvalResult;
    try {
      approvalResult = TaskService.approveTask(task, Number(commissionerId), 'commissioner');
    } catch (serviceError: any) {
      return NextResponse.json(
        err({
          code: ErrorCodes.INVALID_STATUS,
          message: serviceError.message || 'Cannot approve task',
          status: 400,
        }),
        { status: 400 }
      );
    }

    // Check for idempotency (task already approved)
    if (String(task.status).toLowerCase() === 'approved' || task.completed === true) {
      return NextResponse.json(
        ok({
          entities: { task },
          refreshHints: [RefreshHints.TASKS_LIST, RefreshHints.PROJECT_TASKS, RefreshHints.DASHBOARD],
          notificationsQueued: false,
          message: 'Task already approved — no changes applied',
        })
      );
    }

    // 3) Persist via repo
    const updated = await updateTask(taskId, approvalResult.taskPatch);
    if (!updated) {
      return NextResponse.json(
        err({
          code: ErrorCodes.INTERNAL_ERROR,
          message: 'Failed to update task',
          status: 500,
        }),
        { status: 500 }
      );
    }

    const updatedTask = { ...task, ...approvalResult.taskPatch };

    // 4) Log the transition for observability
    logTaskTransition(
      taskId,
      task.status,
      'Approved',
      Number(commissionerId),
      {
        projectId: Number(projectId),
        taskTitle: task.title,
      }
    );

    // 5) Emit centralized event via bus (handlers can auto-gen invoices, send notifs, etc.)
    if (approvalResult.shouldNotify) {
      try {
        await emitBus('task.approved', {
          actorId: Number(commissionerId),
          targetId: Number((task as any).assigneeId ?? (task as any).freelancerId ?? 0),
          projectId: Number(projectId),
          taskId: String(taskId),
          taskTitle: task.title,
        });
      } catch (e) {
        // Do not fail the request if emit fails; log for diagnostics only
        console.warn('[task.approve] bus emit failed:', e);
      }
    }

    return NextResponse.json(
      ok({
        entities: { task: updatedTask },
        refreshHints: [
          RefreshHints.TASKS_LIST,
          RefreshHints.PROJECT_TASKS,
          RefreshHints.INVOICES_LIST,
          RefreshHints.DASHBOARD,
        ],
        notificationsQueued: approvalResult.shouldNotify,
        message: `Task "${task.title ?? taskId}" approved successfully`,
      })
    );
  } catch (error) {
    console.error('[TASK_APPROVE_ERROR]', error);
    return NextResponse.json(
      err({
        code: ErrorCodes.INTERNAL_ERROR,
        message: 'Failed to approve task',
        status: 500,
      }),
      { status: 500 }
    );
  }
}
