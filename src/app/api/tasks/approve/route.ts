import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getTaskById, updateTask } from '@/app/api/payments/repos/tasks-repo';
import { emit as emitBus } from '@/lib/events/bus';

export async function POST(request: Request) {
  try {
    // 🔒 Auth (optional—keep if tasks are commissioner-only actions)
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ ok: false, error: 'Unauthorized' }, { status: 401 });
    }

    const { taskId, projectId, commissionerId } = await request.json();

    if (!taskId || !projectId || !commissionerId) {
      return NextResponse.json({ ok: false, error: 'Missing required fields: taskId, projectId, commissionerId' }, { status: 400 });
    }

    // Only the logged-in commissioner can approve
    if (Number(commissionerId) !== Number(session.user.id)) {
      return NextResponse.json({ ok: false, error: 'Unauthorized: commissioner mismatch' }, { status: 403 });
    }

    // 1) Read from repo
    const task = await getTaskById(taskId);
    if (!task) {
      return NextResponse.json({ ok: false, error: 'Task not found' }, { status: 404 });
    }

    if (Number(task.projectId) !== Number(projectId)) {
      return NextResponse.json({ ok: false, error: 'Task does not belong to provided projectId' }, { status: 400 });
    }

    // 2) Service rule (inline minimal): idempotency + normalization
    const alreadyApproved = String(task.status).toLowerCase() === 'approved' || task.completed === true;
    if (alreadyApproved) {
      // No-op, return current task
      const requestId = crypto.randomUUID();
      return NextResponse.json({
        ok: true,
        requestId,
        message: 'Task already approved — no changes applied',
        entities: { task },
        refreshHints: [`tasks:${projectId}:board`],
      });
    }

    const nowISO = new Date().toISOString();

    // 3) Persist via repo (approved + completed)
    const patch = {
      status: 'approved' as const,
      completed: true,
      approvedAt: nowISO,
      approvedBy: Number(commissionerId),
      updatedAt: nowISO,
    };

    const updated = await updateTask(taskId, patch);
    if (!updated) {
      return NextResponse.json({ ok: false, error: 'Failed to update task' }, { status: 500 });
    }

    const updatedTask = { ...task, ...patch };

    // 4) Emit centralized event via bus (handlers can auto-gen invoices, send notifs, etc.)
    try {
      await emitBus('task.approved', {
        actorId: Number(commissionerId),
        targetId: Number((task as any).assigneeId ?? (task as any).freelancerId ?? 0),
        projectId: Number(projectId),
        taskId: String(taskId),
        taskTitle: task.title,
      });
    } catch (e) {
      // Do not fail the request if emit fails; log for diagnostics only
      console.warn('[task.approve] bus emit failed:', e);
    }

    const requestId = crypto.randomUUID();
    return NextResponse.json({
      ok: true,
      requestId,
      message: `Task "${task.title ?? taskId}" approved successfully`,
      entities: { task: updatedTask },
      refreshHints: [`tasks:${projectId}:board`, 'invoices:list'],
      notificationsQueued: true,
    });
  } catch (error) {
    console.error('[TASK_APPROVE_ERROR]', error);
    return NextResponse.json({ ok: false, error: 'Failed to approve task' }, { status: 500 });
  }
}
