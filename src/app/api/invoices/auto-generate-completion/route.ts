import { NextResponse } from 'next/server';
import path from 'path';
import { promises as fs } from 'fs';
import { eventLogger } from '../../../../lib/events/event-logger';
import { readProject } from '../../../../lib/projects-utils';
import { getAllInvoices, saveInvoice } from '../../../../lib/invoice-storage';
import { getInitialInvoiceStatus } from '../../../../lib/invoice-status-definitions';

/**
 * Auto-Generate Invoice for Completion-Based Projects
 * 
 * This endpoint is called when a task is approved in a completion-based project.
 * It automatically creates an invoice for the approved task based on the project's
 * budget allocation.
 * 
 * PAYMENT GATEWAY INTEGRATION:
 * When payment gateways are integrated, this endpoint should also:
 * 1. Create payment intent in the gateway
 * 2. Generate payment link for commissioner
 * 3. Set up automatic payout to freelancer upon payment
 */

export async function POST(request: Request) {
  try {
    const { 
      taskId, 
      projectId, 
      freelancerId, 
      commissionerId, 
      taskTitle, 
      projectTitle 
    } = await request.json();

    if (!taskId || !projectId || !freelancerId || !commissionerId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Load data
    const [invoices, project] = await Promise.all([
      getAllInvoices(), // Use hierarchical storage for invoices
      readProject(projectId) // Use hierarchical storage for projects
    ]);

    // invoices is already parsed from hierarchical storage
    // project is already the specific project we need
    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }

    // Verify this is a completion-based project
    if (project.invoicingMethod !== 'completion') {
      return NextResponse.json({ 
        error: 'This endpoint is only for completion-based projects' 
      }, { status: 400 });
    }

    // Check if invoice already exists for this task
    const existingInvoice = invoices.find((inv: any) =>
      inv.projectId === projectId &&
      inv.milestoneDescription?.includes(taskTitle) &&
      inv.freelancerId === freelancerId
    );

    if (existingInvoice) {
      return NextResponse.json({ 
        message: 'Invoice already exists for this task',
        invoiceNumber: existingInvoice.invoiceNumber
      });
    }

    // Calculate completion-based amount with improved budget handling
    const totalBudget = project.totalBudget || project.budget?.upper || project.budget?.lower || 5000; // Use budget from project
    const upfrontCommitment = project.upfrontCommitment || project.upfrontAmount || 0;
    const remainingBudget = totalBudget - upfrontCommitment;
    const totalTasks = project.totalTasks || 1;
    const amountPerTask = Math.round((remainingBudget / totalTasks) * 100) / 100;

    console.log(`💰 Budget calculation for project ${projectId}:`, {
      totalBudget,
      upfrontCommitment,
      remainingBudget,
      totalTasks,
      amountPerTask
    });

    // Generate invoice number
    const invoiceNumber = `COMP_${projectId}_${taskId}_${Date.now()}`;

    // Create new invoice with proper status system
    const initialStatus = getInitialInvoiceStatus('auto_completion', true);
    const newInvoice = {
      invoiceNumber,
      freelancerId,
      projectId,
      commissionerId,
      projectTitle: projectTitle || project.title,
      milestoneDescription: taskTitle,
      milestoneNumber: taskId, // Use taskId as milestone number for completion-based
      issueDate: new Date().toISOString().split('T')[0],
      dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 14 days
      totalAmount: amountPerTask,
      status: initialStatus, // Use proper status system ('sent' for auto-completion)
      invoiceType: 'auto_completion' as const, // Track invoice type
      milestones: [
        {
          description: taskTitle,
          rate: amountPerTask,
          taskId: taskId,
          approvedAt: new Date().toISOString()
        }
      ],
      // Legacy fields for backward compatibility
      isAutoGenerated: true,
      invoicingMethod: 'completion',
      generatedAt: new Date().toISOString(),
      sentDate: new Date().toISOString(), // Auto-completion invoices are immediately sent
      // TODO: Add payment gateway fields when integrated
      // paymentGateway: {
      //   provider: 'stripe',
      //   paymentIntentId: null,
      //   paymentLink: null
      // }
    };

    // Save invoice using hierarchical storage
    await saveInvoice(newInvoice);

    // Log invoice creation event
    try {
      await eventLogger.logEvent({
        id: `invoice_auto_generated_${invoiceNumber}_${Date.now()}`,
        timestamp: new Date().toISOString(),
        type: 'invoice_sent',
        notificationType: 40, // NOTIFICATION_TYPES.INVOICE_SENT
        actorId: freelancerId,
        targetId: commissionerId,
        entityType: 5, // ENTITY_TYPES.INVOICE
        entityId: invoiceNumber,
        metadata: {
          invoiceNumber,
          projectTitle: projectTitle || project.title,
          taskTitle,
          amount: amountPerTask,
          invoicingMethod: 'completion',
          autoGenerated: true
        },
        context: {
          projectId,
          taskId,
          invoiceNumber
        }
      });
    } catch (eventError) {
      console.error('Failed to log invoice creation event:', eventError);
    }

    return NextResponse.json({
      success: true,
      invoice: newInvoice,
      message: `Auto-generated completion invoice ${invoiceNumber} for task: ${taskTitle}`,
      calculation: {
        totalBudget,
        upfrontCommitment,
        remainingBudget,
        totalTasks,
        amountPerTask
      }
    });

  } catch (error) {
    console.error('Error auto-generating completion invoice:', error);
    return NextResponse.json({ error: 'Failed to auto-generate invoice' }, { status: 500 });
  }
}
