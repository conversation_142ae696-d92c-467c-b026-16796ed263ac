// src/lib/log/transitions.ts
// Observability helper for tracking status transitions across entities
// Provides structured logging for debugging and monitoring

export interface TransitionLog {
  entity: string;
  id: string | number;
  fromStatus: string;
  toStatus: string;
  actorId: string | number;
  timestamp: string;
  requestId?: string;
  metadata?: Record<string, unknown>;
}

/**
 * Log a status transition for observability
 * @param entity - Entity type (e.g., 'invoice', 'task', 'project', 'withdrawal')
 * @param id - Entity identifier
 * @param fromStatus - Previous status
 * @param toStatus - New status
 * @param actorId - User who initiated the transition
 * @param metadata - Additional context data
 */
export function logTransition(
  entity: string,
  id: string | number,
  fromStatus: string,
  toStatus: string,
  actorId: number | string,
  metadata?: Record<string, unknown>
): void {
  const timestamp = new Date().toISOString();
  
  const logEntry: TransitionLog = {
    entity,
    id,
    fromStatus,
    toStatus,
    actorId,
    timestamp,
    metadata,
  };

  // Structured console logging for development and debugging
  console.log(
    `[transition] ${entity}#${id}: ${fromStatus} -> ${toStatus} by ${actorId} @ ${timestamp}`,
    metadata ? { metadata } : ''
  );

  // In production, this could be extended to:
  // - Send to structured logging service (e.g., DataDog, CloudWatch)
  // - Store in audit log database
  // - Trigger monitoring alerts for critical transitions
  // - Generate metrics for transition success rates
}

/**
 * Log an invoice status transition
 */
export function logInvoiceTransition(
  invoiceNumber: string,
  fromStatus: string,
  toStatus: string,
  actorId: number | string,
  metadata?: {
    projectId?: number;
    amount?: number;
    currency?: string;
    integration?: string;
    transactionId?: string;
  }
): void {
  logTransition('invoice', invoiceNumber, fromStatus, toStatus, actorId, metadata);
}

/**
 * Log a task status transition
 */
export function logTaskTransition(
  taskId: number | string,
  fromStatus: string,
  toStatus: string,
  actorId: number | string,
  metadata?: {
    projectId?: number;
    taskTitle?: string;
    rejectionReason?: string;
  }
): void {
  logTransition('task', taskId, fromStatus, toStatus, actorId, metadata);
}

/**
 * Log a project status transition
 */
export function logProjectTransition(
  projectId: number | string,
  fromStatus: string,
  toStatus: string,
  actorId: number | string,
  metadata?: {
    projectTitle?: string;
    gigId?: number;
    reason?: string;
  }
): void {
  logTransition('project', projectId, fromStatus, toStatus, actorId, metadata);
}

/**
 * Log a withdrawal status transition
 */
export function logWithdrawalTransition(
  withdrawalId: string,
  fromStatus: string,
  toStatus: string,
  actorId: number | string,
  metadata?: {
    amount?: number;
    currency?: string;
    method?: string;
    reason?: string;
  }
): void {
  logTransition('withdrawal', withdrawalId, fromStatus, toStatus, actorId, metadata);
}

/**
 * Log a wallet balance change
 */
export function logWalletChange(
  userId: number,
  userType: 'freelancer' | 'commissioner',
  changeType: 'credit' | 'debit' | 'hold' | 'release',
  amount: number,
  currency: string,
  actorId: number | string,
  metadata?: {
    reason?: string;
    transactionId?: string;
    invoiceNumber?: string;
    withdrawalId?: string;
    previousBalance?: number;
    newBalance?: number;
  }
): void {
  const walletId = `${userType}-${userId}-${currency}`;
  logTransition('wallet', walletId, 'balance_change', changeType, actorId, {
    amount,
    currency,
    ...metadata,
  });
}
