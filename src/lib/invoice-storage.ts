// src/lib/invoice-storage.ts
import { readFile, writeFile, readdir, stat } from 'fs/promises';
import path from 'path';

import { InvoiceStatus, InvoiceType } from './invoice-status-definitions';

export interface Invoice {
  invoiceNumber: string;
  freelancerId: number | string;
  projectId: number | null;
  commissionerId: number;
  projectTitle: string;
  milestoneDescription?: string;
  milestoneNumber?: number;
  issueDate: string;
  dueDate: string;
  totalAmount: number;
  status: InvoiceStatus; // Use the well-defined status type
  invoiceType?: InvoiceType; // Track invoice type (manual, auto_milestone, auto_completion)
  milestones: Array<{
    description: string;
    rate: number;
    title?: string;
    taskId?: number;
  }>;
  isCustomProject?: boolean;
  isManualInvoice?: boolean;
  parentInvoiceNumber?: string;
  paidDate?: string;
  sentDate?: string;
  createdAt?: string;
  updatedAt?: string;
  paymentDetails?: {
    paymentId: string;
    paymentMethod: string;
    platformFee: number;
    freelancerAmount: number;
    currency: string;
    processedAt: string;
  };
  // Auto-milestone specific fields
  autoPaymentAttempts?: number;
  lastPaymentAttempt?: string;
  nextRetryDate?: string;
  paymentFailureReason?: string;
  // Legacy fields for backward compatibility
  id?: number;
  autoGenerated?: boolean;
  paidAmount?: number;
  sentAt?: string;
  versions?: any[];
}

// Helper function to parse date from various formats
function parseInvoiceDate(dateStr: string): Date {
  if (!dateStr) return new Date();
  
  // Handle ISO string format (e.g., "2025-07-27T16:55:28.237Z")
  if (dateStr.includes('T')) {
    return new Date(dateStr);
  }
  
  // Handle simple date format (e.g., "2025-06-01")
  return new Date(dateStr + 'T00:00:00.000Z');
}

// Helper function to format date parts
function getDateParts(date: Date): { year: string; month: string; day: string } {
  if (!date || isNaN(date.getTime())) {
    date = new Date();
  }
  
  const year = date.getFullYear().toString();
  const month = date.toLocaleString('en-US', { month: 'long' }); // e.g., "July"
  const day = date.getDate().toString().padStart(2, '0'); // e.g., "01"
  
  return { year, month, day };
}

// Helper function to ensure directory exists
async function ensureDir(dirPath: string): Promise<void> {
  try {
    await stat(dirPath);
  } catch {
    const { mkdir } = await import('fs/promises');
    await mkdir(dirPath, { recursive: true });
  }
}

// Get the file path for an invoice
function getInvoiceFilePath(invoice: Invoice): string {
  const issueDate = parseInvoiceDate(invoice.issueDate);
  const { year, month, day } = getDateParts(issueDate);
  const projectId = invoice.projectId ? invoice.projectId.toString() : 'custom';
  
  return path.join(
    process.cwd(),
    'data',
    'invoices',
    year,
    month,
    day,
    projectId,
    'invoice.json'
  );
}

// Get directory path for invoices
function getInvoiceDirectoryPath(invoice: Invoice): string {
  const issueDate = parseInvoiceDate(invoice.issueDate);
  const { year, month, day } = getDateParts(issueDate);
  const projectId = invoice.projectId ? invoice.projectId.toString() : 'custom';
  
  return path.join(
    process.cwd(),
    'data',
    'invoices',
    year,
    month,
    day,
    projectId
  );
}

// Save an invoice to the hierarchical structure
export async function saveInvoice(invoice: Invoice): Promise<void> {
  const dirPath = getInvoiceDirectoryPath(invoice);
  await ensureDir(dirPath);
  
  // Check if invoice already exists
  const filePath = getInvoiceFilePath(invoice);
  let filename = 'invoice.json';
  
  try {
    const existingData = await readFile(filePath, 'utf-8');
    const existingInvoice = JSON.parse(existingData);
    
    if (existingInvoice.invoiceNumber !== invoice.invoiceNumber) {
      // Different invoice, create unique filename
      let counter = 1;
      while (true) {
        filename = `invoice-${counter}.json`;
        const newFilePath = path.join(dirPath, filename);
        try {
          const data = await readFile(newFilePath, 'utf-8');
          const existing = JSON.parse(data);
          if (existing.invoiceNumber === invoice.invoiceNumber) {
            filename = `invoice-${counter}.json`;
            break;
          }
          counter++;
        } catch {
          break;
        }
      }
    }
  } catch {
    // File doesn't exist, use default filename
  }
  
  const finalPath = path.join(dirPath, filename);
  await writeFile(finalPath, JSON.stringify(invoice, null, 2));
}

// Get all invoices (with optional filters)
export async function getAllInvoices(filters?: {
  freelancerId?: number | string;
  commissionerId?: number;
  projectId?: number;
  status?: string;
  startDate?: string;
  endDate?: string;
}): Promise<Invoice[]> {
  const invoices: Invoice[] = [];
  const baseDir = path.join(process.cwd(), 'data', 'invoices');
  
  try {
    await stat(baseDir);
  } catch {
    return [];
  }
  
  async function scanDirectory(dirPath: string): Promise<void> {
    try {
      const entries = await readdir(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        
        if (entry.isDirectory()) {
          await scanDirectory(fullPath);
        } else if (entry.name.endsWith('.json')) {
          try {
            const data = await readFile(fullPath, 'utf-8');
            const invoice = JSON.parse(data) as Invoice;
            
            // Apply filters
            if (filters) {
              if (filters.freelancerId && invoice.freelancerId.toString() !== filters.freelancerId.toString()) continue;
              if (filters.commissionerId && invoice.commissionerId !== filters.commissionerId) continue;
              if (filters.projectId && invoice.projectId !== filters.projectId) continue;
              if (filters.status && invoice.status !== filters.status) continue;
              
              if (filters.startDate || filters.endDate) {
                const invoiceDate = parseInvoiceDate(invoice.issueDate);
                if (filters.startDate && invoiceDate < new Date(filters.startDate)) continue;
                if (filters.endDate && invoiceDate > new Date(filters.endDate)) continue;
              }
            }
            
            invoices.push(invoice);
          } catch (error) {
            console.error(`Error reading invoice file ${fullPath}:`, error);
          }
        }
      }
    } catch (error) {
      console.error(`Error scanning directory ${dirPath}:`, error);
    }
  }
  
  await scanDirectory(baseDir);
  
  // Sort by issue date (newest first)
  return invoices.sort((a, b) => {
    const dateA = parseInvoiceDate(a.issueDate);
    const dateB = parseInvoiceDate(b.issueDate);
    return dateB.getTime() - dateA.getTime();
  });
}

// Get invoice by invoice number
export async function getInvoiceByNumber(invoiceNumber: string): Promise<Invoice | null> {
  const allInvoices = await getAllInvoices();
  return allInvoices.find(inv => inv.invoiceNumber === invoiceNumber) || null;
}

// Get invoices by project ID
export async function getInvoicesByProjectId(projectId: number): Promise<Invoice[]> {
  return getAllInvoices({ projectId });
}

// Get invoices by freelancer ID
export async function getInvoicesByFreelancerId(freelancerId: number | string): Promise<Invoice[]> {
  return getAllInvoices({ freelancerId });
}

// Get invoices by commissioner ID
export async function getInvoicesByCommissionerId(commissionerId: number): Promise<Invoice[]> {
  return getAllInvoices({ commissionerId });
}

// Update an existing invoice
export async function updateInvoice(invoiceNumber: string, updates: Partial<Invoice>): Promise<boolean> {
  const invoice = await getInvoiceByNumber(invoiceNumber);
  if (!invoice) return false;
  
  const updatedInvoice = { ...invoice, ...updates, updatedAt: new Date().toISOString() };
  await saveInvoice(updatedInvoice);
  return true;
}

// Delete an invoice
export async function deleteInvoice(invoiceNumber: string): Promise<boolean> {
  const invoice = await getInvoiceByNumber(invoiceNumber);
  if (!invoice) return false;
  
  const filePath = getInvoiceFilePath(invoice);
  try {
    const { unlink } = await import('fs/promises');
    await unlink(filePath);
    return true;
  } catch {
    return false;
  }
}

// Get invoices by date range
export async function getInvoicesByDateRange(startDate: string, endDate: string): Promise<Invoice[]> {
  return getAllInvoices({ startDate, endDate });
}

// Legacy compatibility: Get all invoices in the old format (array)
export async function getLegacyInvoicesArray(): Promise<Invoice[]> {
  return getAllInvoices();
}
