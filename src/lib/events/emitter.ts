import { EventData, EventType } from '@/lib/events/event-logger'
import { NotificationStorage } from '@/lib/notifications/notification-storage'

// Try to import a numeric enum if it exists; fall back safely
// eslint-disable-next-line @typescript-eslint/no-var-requires
let NotificationTypeEnum: any = undefined
try {
  // Dynamically require to avoid hard coupling if not exported
  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  NotificationTypeEnum = require('@/lib/events/event-logger').NotificationType
} catch {
  // noop
}

let EventTypeEnum: any = undefined
try {
  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  EventTypeEnum = require('@/lib/events/event-logger').EventType
} catch {
  // noop
}

/**
 * Emits a generic event by appending it to NotificationStorage.
 * Automatically generates an id and timestamp.
 * Accepts numeric actor/target ids to satisfy EventData.
 */
export function emitEvent(event: Omit<EventData, 'id' | 'timestamp'> | any) {
  const newEvent = {
    ...event,
    id: crypto.randomUUID(),
    timestamp: new Date().toISOString(),
  } as unknown as EventData;

  // Call the storage using whichever method exists (append/add/push/write)
  const storageAny = NotificationStorage as any
  if (typeof storageAny.append === 'function') {
    storageAny.append(newEvent)
  } else if (typeof storageAny.add === 'function') {
    storageAny.add(newEvent)
  } else if (typeof storageAny.push === 'function') {
    storageAny.push(newEvent)
  } else if (typeof storageAny.write === 'function') {
    storageAny.write(newEvent)
  } else {
    // Last resort to keep dev moving; avoid runtime crash
    console.warn('[emitEvent] NotificationStorage has no append/add/push/write method')
  }
}

/**
 * Emits an 'invoice_paid' event.
 * Ensures numeric ids and numeric notification type.
 */
export function emitInvoicePaid(
  actorId: string | number,
  targetId: string | number,
  projectId: string | number,
  invoiceNumber: string,
  amount: number,
  projectTitle?: string
) {
  const actorNum = Number(actorId)
  const targetNum = Number(targetId)
  const notifType: number = (NotificationTypeEnum?.Success as number) ?? 1 // fallback to 1

  const eventType: EventType = (EventTypeEnum?.InvoicePaid as EventType) ?? ('invoice_paid' as unknown as EventType);
  const projectIdNum = Number(projectId);

  emitEvent({
    type: eventType as any,
    actorId: actorNum,
    targetId: targetNum,
    context: {
      projectId: projectIdNum as any,
      invoiceNumber,
    } as any,
    metadata: {
      amount,
      projectTitle,
    } as any,
    notificationType: notifType as any,
  } as any)
}

/**
 * Emits a 'task_approved' event.
 * Mirrors invoice helper, keeps types relaxed to match local EventData shape.
 */
export function emitTaskApproved(
  actorId: string | number,
  targetId: string | number,
  projectId: string | number,
  taskId: string | number,
  taskTitle?: string
) {
  const actorNum = Number(actorId);
  const targetNum = Number(targetId);
  const notifType: number = (NotificationTypeEnum?.Success as number) ?? 1; // success/info

  const eventType: EventType = (EventTypeEnum?.TaskApproved as EventType) ?? ('task_approved' as unknown as EventType);
  const projectIdNum = Number(projectId);

  emitEvent({
    type: eventType as any,
    actorId: actorNum,
    targetId: targetNum,
    context: {
      projectId: projectIdNum as any,
      taskId: String(taskId),
    } as any,
    metadata: {
      taskTitle,
    } as any,
    notificationType: notifType as any,
  } as any);
}
