// src/lib/http/envelope.ts
// Standard response envelope for all API routes
// Provides consistent structure for success/error responses with observability

export interface SuccessEnvelope<T extends Record<string, any> = Record<string, any>> {
  ok: true;
  requestId: string;
  entities: T;
  refreshHints: string[];
  notificationsQueued: boolean;
  message?: string;
}

export interface ErrorEnvelope {
  ok: false;
  requestId: string;
  code: string;
  message: string;
  status: number;
}

export type ApiEnvelope<T extends Record<string, any> = Record<string, any>> = 
  | SuccessEnvelope<T> 
  | ErrorEnvelope;

/**
 * Create a standardized success response envelope
 * @param payload - Response data including entities, refresh hints, etc.
 * @returns Standardized success envelope
 */
export function ok<T extends Record<string, any>>(payload: {
  entities?: T;
  refreshHints?: string[];
  notificationsQueued?: boolean;
  message?: string;
}): SuccessEnvelope<T> {
  const requestId = crypto.randomUUID();
  return {
    ok: true,
    requestId,
    entities: payload.entities ?? {} as T,
    refreshHints: payload.refreshHints ?? [],
    notificationsQueued: !!payload.notificationsQueued,
    message: payload.message,
  };
}

/**
 * Create a standardized error response envelope
 * @param payload - Error details including code, message, and status
 * @returns Standardized error envelope
 */
export function err(payload: { 
  code: string; 
  message: string; 
  status?: number; 
}): ErrorEnvelope {
  const requestId = crypto.randomUUID();
  return {
    ok: false,
    requestId,
    code: payload.code,
    message: payload.message,
    status: payload.status ?? 400,
  };
}

/**
 * Common refresh hints for different domains
 */
export const RefreshHints = {
  // Wallet-related updates
  WALLET_SUMMARY: 'wallet:summary',
  WALLET_TRANSACTIONS: 'wallet:transactions',
  
  // Invoice-related updates
  INVOICES_LIST: 'invoices:list',
  INVOICE_DETAIL: 'invoice:detail',
  
  // Transaction-related updates
  TRANSACTIONS_LIST: 'transactions:list',
  TRANSACTION_DETAIL: 'transaction:detail',
  
  // Project-related updates
  PROJECTS_OVERVIEW: 'projects:overview',
  PROJECT_DETAIL: 'project:detail',
  PROJECT_TASKS: 'project:tasks',
  
  // Task-related updates
  TASKS_LIST: 'tasks:list',
  TASK_DETAIL: 'task:detail',
  
  // Notification updates
  NOTIFICATIONS: 'notifications',
  
  // Dashboard updates
  DASHBOARD: 'dashboard',
  
  // Gig-related updates
  GIGS_LIST: 'gigs:list',
  GIG_DETAIL: 'gig:detail',
} as const;

/**
 * Common error codes for consistent error handling
 */
export const ErrorCodes = {
  // Authentication/Authorization
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  INVALID_SESSION: 'INVALID_SESSION',
  
  // Validation
  INVALID_INPUT: 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  INVALID_FORMAT: 'INVALID_FORMAT',
  
  // Business Logic
  INVALID_STATUS: 'INVALID_STATUS',
  INSUFFICIENT_FUNDS: 'INSUFFICIENT_FUNDS',
  DUPLICATE_REQUEST: 'DUPLICATE_REQUEST',
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  
  // System
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  RATE_LIMITED: 'RATE_LIMITED',
} as const;
